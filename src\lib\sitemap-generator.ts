import fs from "fs";
import path from "path";
import { MetadataRoute } from "next";

export interface SitemapRoute {
  path: string;
  lastmod?: string;
  changefreq?:
    | "always"
    | "hourly"
    | "daily"
    | "weekly"
    | "monthly"
    | "yearly"
    | "never";
  priority?: string;
}

export interface SitemapConfig {
  baseUrl: string;
  routes: SitemapRoute[];
  excludePaths?: string[];
}

export interface BlogPost {
  id: string;
  slug: string;
  title: string;
  publishedAt: string;
  updatedAt?: string;
  status: "published" | "draft";
}

export interface PortfolioItem {
  id: string;
  title: string;
}

export interface JobPosting {
  id: string;
  slug: string;
  title: string;
  department: string;
  location: string;
  type: "full-time" | "part-time" | "contract" | "internship";
  status: "active" | "closed" | "draft";
  publishedAt: string;
  updatedAt?: string;
}

/**
 * Get blog posts from data file
 */
export function getBlogPosts(): BlogPost[] {
  try {
    const blogPostsPath = path.join(
      process.cwd(),
      "public/data/blog-posts.json"
    );
    if (!fs.existsSync(blogPostsPath)) {
      console.warn("Blog posts data file not found");
      return [];
    }

    const data = fs.readFileSync(blogPostsPath, "utf8");
    const posts = JSON.parse(data) as BlogPost[];

    return posts.filter((post) => post.status === "published");
  } catch (error) {
    console.error("Error reading blog posts:", error);
    return [];
  }
}

/**
 * Get portfolio items from data file
 */
export function getPortfolioItems(): PortfolioItem[] {
  try {
    const portfolioPath = path.join(
      process.cwd(),
      "public/data/portfolio.json"
    );
    if (!fs.existsSync(portfolioPath)) {
      console.warn("Portfolio data file not found");
      return [];
    }

    const data = fs.readFileSync(portfolioPath, "utf8");
    return JSON.parse(data) as PortfolioItem[];
  } catch (error) {
    console.error("Error reading portfolio items:", error);
    return [];
  }
}

/**
 * Get job postings from data file
 */
export function getJobPostings(): JobPosting[] {
  try {
    const jobsPath = path.join(process.cwd(), "public/data/jobs.json");
    if (!fs.existsSync(jobsPath)) {
      console.warn("Jobs data file not found");
      return [];
    }

    const data = fs.readFileSync(jobsPath, "utf8");
    const jobs = JSON.parse(data) as JobPosting[];

    return jobs.filter((job) => job.status === "active");
  } catch (error) {
    console.error("Error reading job postings:", error);
    return [];
  }
}

/**
 * Convert SitemapRoute to MetadataRoute.Sitemap format
 */
export function convertToMetadataRoute(
  routes: SitemapRoute[],
  baseUrl: string = "https://lunarcubes.com.np"
): MetadataRoute.Sitemap {
  return routes.map((route) => ({
    url: `${baseUrl}${route.path}`,
    lastModified: route.lastmod ? new Date(route.lastmod) : new Date(),
    changeFrequency: route.changefreq,
    priority: route.priority ? parseFloat(route.priority) : undefined,
  }));
}

/**
 * Generate sitemap routes dynamically
 */
export function generateSitemapRoutes(): SitemapRoute[] {
  const routes: SitemapRoute[] = [];
  const currentDate = new Date().toISOString();

  // Static routes with their configurations
  const staticRoutes = [
    { path: "/", priority: "1.0", changefreq: "weekly" as const },
    { path: "/about", priority: "0.8", changefreq: "monthly" as const },
    { path: "/services", priority: "0.9", changefreq: "weekly" as const },
    { path: "/portfolio", priority: "0.8", changefreq: "weekly" as const },
    { path: "/contact", priority: "0.7", changefreq: "monthly" as const },
    { path: "/careers", priority: "0.8", changefreq: "weekly" as const },
    { path: "/blog", priority: "0.9", changefreq: "daily" as const },
  ];

  // Add static routes
  staticRoutes.forEach((route) => {
    routes.push({
      ...route,
      lastmod: currentDate.split("T")[0],
    });
  });

  // Add blog post routes
  const blogPosts = getBlogPosts();
  blogPosts.forEach((post) => {
    routes.push({
      path: `/blog/${post.slug}`,
      lastmod: post.updatedAt || post.publishedAt,
      changefreq: "weekly",
      priority: "0.7",
    });
  });

  // Add portfolio routes
  const portfolioItems = getPortfolioItems();
  portfolioItems.forEach((item) => {
    routes.push({
      path: `/portfolio/${item.id}`,
      lastmod: currentDate.split("T")[0],
      changefreq: "monthly",
      priority: "0.6",
    });
  });

  // Add job posting routes
  const jobPostings = getJobPostings();
  jobPostings.forEach((job) => {
    routes.push({
      path: `/careers/${job.slug}`,
      lastmod: job.updatedAt || job.publishedAt,
      changefreq: "weekly",
      priority: "0.6",
    });
  });

  return routes;
}

/**
 * Generate XML sitemap content
 */
export function generateSitemapXML(config: SitemapConfig): string {
  let xml = '<?xml version="1.0" encoding="UTF-8"?>\n';
  xml += '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n';

  // Filter out excluded paths
  const filteredRoutes = config.routes.filter(
    (route) =>
      !config.excludePaths?.some((excludePath) =>
        route.path.startsWith(excludePath)
      )
  );

  // Sort routes by priority (descending) then alphabetically
  const sortedRoutes = filteredRoutes.sort((a, b) => {
    const priorityA = parseFloat(a.priority || "0.5");
    const priorityB = parseFloat(b.priority || "0.5");

    if (priorityA !== priorityB) {
      return priorityB - priorityA;
    }

    return a.path.localeCompare(b.path);
  });

  sortedRoutes.forEach((route) => {
    xml += "  <url>\n";
    xml += `    <loc>${config.baseUrl}${route.path}</loc>\n`;

    if (route.lastmod) {
      // Ensure lastmod is in proper format (YYYY-MM-DD)
      const lastmod = route.lastmod.includes("T")
        ? route.lastmod.split("T")[0]
        : route.lastmod;
      xml += `    <lastmod>${lastmod}</lastmod>\n`;
    }

    if (route.changefreq) {
      xml += `    <changefreq>${route.changefreq}</changefreq>\n`;
    }

    if (route.priority) {
      xml += `    <priority>${route.priority}</priority>\n`;
    }

    xml += "  </url>\n";
  });

  xml += "</urlset>\n";
  return xml;
}

/**
 * Generate and save sitemap to public directory
 */
export function generateAndSaveSitemap(
  baseUrl: string = "https://lunarcubes.com.np"
): void {
  try {
    const routes = generateSitemapRoutes();

    const config: SitemapConfig = {
      baseUrl,
      routes,
      excludePaths: ["/api", "/admin", "/_next"],
    };

    const sitemapXML = generateSitemapXML(config);

    const outputPath = path.join(process.cwd(), "public/sitemap.xml");
    fs.writeFileSync(outputPath, sitemapXML, "utf8");

    console.log(`✅ Sitemap generated with ${routes.length} URLs`);
    console.log(`📍 Saved to: ${outputPath}`);
  } catch (error) {
    console.error("❌ Error generating sitemap:", error);
    throw error;
  }
}

/**
 * Generate robots.txt content
 */
export function generateRobotsTxt(
  baseUrl: string = "https://lunarcubes.com.np"
): string {
  return `User-agent: *
Allow: /

# Sitemap
Sitemap: ${baseUrl}/sitemap.xml

# Disallow admin and private areas
Disallow: /admin/
Disallow: /api/
Disallow: /_next/
Disallow: /private/

# Allow common crawlers
User-agent: Googlebot
Allow: /

User-agent: Bingbot
Allow: /

# Crawl delay
Crawl-delay: 1
`;
}

/**
 * Save robots.txt file
 */
export function saveRobotsTxt(
  baseUrl: string = "https://lunarcubes.com.np"
): void {
  try {
    const robotsContent = generateRobotsTxt(baseUrl);
    const outputPath = path.join(process.cwd(), "public/robots.txt");
    fs.writeFileSync(outputPath, robotsContent, "utf8");
    console.log(`🤖 robots.txt saved to: ${outputPath}`);
  } catch (error) {
    console.error("❌ Error generating robots.txt:", error);
    throw error;
  }
}
