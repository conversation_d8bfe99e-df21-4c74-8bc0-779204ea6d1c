import { MetadataRoute } from "next";
import { generateSitemapRoutes, convertToMetadataRoute } from "@/lib/sitemap-generator";

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || "https://lunarcubes.com.np";
  
  // Generate dynamic routes
  const routes = generateSitemapRoutes();
  
  // Convert to MetadataRoute format
  return convertToMetadataRoute(routes, baseUrl);
}
